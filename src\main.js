import './style.css'
import { TimerPresenter } from './presenters/TimerPresenter.js'
import { MusicPresenter } from './presenters/MusicPresenter.js'
import { SettingsPresenter } from './presenters/SettingsPresenter.js'
import { ReportPresenter } from './presenters/ReportPresenter.js'
// TODO: Task List feature
// import { TaskListPresenter } from './presenters/TaskListPresenter.js'
// import { TaskModel } from './models/TaskModel.js'
// import { TaskListView } from './views/TaskListView.js'

class PomodoroApp {
  constructor() {
    this.timerPresenter = new TimerPresenter()
    this.musicPresenter = new MusicPresenter()
    this.settingsPresenter = new SettingsPresenter()
    this.reportPresenter = new ReportPresenter()

    // TODO: Task list components
    // this.taskModel = new TaskModel()
    // this.taskListView = new TaskListView()
    // this.taskListPresenter = new TaskListPresenter(this.taskModel, this.taskListView)

    this.timerPresenter.setMusicPresenter(this.musicPresenter)
    this.init()
  }

  async init() {
    // Set default light theme
    document.body.setAttribute('data-theme', 'light')

    this.settingsPresenter.init()
    this.reportPresenter.init()
    this.timerPresenter.init()

    // Wire up presenter dependencies
    this.timerPresenter.setSettingsPresenter(this.settingsPresenter)
    this.timerPresenter.setReportPresenter(this.reportPresenter)

    this.connectPresenters()
    await this.initializeMusicTracks()
    await this.requestNotificationPermission()

    // Restore any incomplete session from previous session
    this.restoreIncompleteSession()
  }

  /**
   * Initialize music tracks
   */
  async initializeMusicTracks() {
    try {
      await this.musicPresenter.loadMusicTracks()
    } catch (error) {
      console.error('Failed to initialize music tracks:', error)
    }
  }

  /**
   * Request notification permission
   */
  async requestNotificationPermission() {
    try {
      const timerModel = this.timerPresenter.model
      if (timerModel && timerModel.notificationManager) {
        const granted = await timerModel.notificationManager.requestPermission()
        if (granted) {
          console.log('✅ Notification permission granted')
        } else {
          console.log('⚠️ Notification permission denied - notifications will not be shown')
        }
      }
    } catch (error) {
      console.error('Failed to request notification permission:', error)
    }
  }

  connectPresenters() {
    // Music control based on session state
    this.timerPresenter.on('sessionStart', (sessionType) => {
      if (sessionType === 'work' && this.musicPresenter.isEnabled()) {
        this.musicPresenter.startMusic()
      } else {
        this.musicPresenter.stopMusic()
      }
    })

    this.timerPresenter.on('sessionEnd', (sessionType) => {
      this.musicPresenter.stopMusic()
    })

    this.timerPresenter.on('timerPause', () => {
      this.musicPresenter.pauseMusic()
    })

    this.timerPresenter.on('timerResume', () => {
      const currentSession = this.timerPresenter.getCurrentSessionType()
      if (currentSession === 'work' && this.musicPresenter.isEnabled()) {
        this.musicPresenter.resumeMusic()
      }
    })

    this.timerPresenter.on('settingsRequested', () => {
      this.settingsPresenter.showSettings()
    })

    // TODO: Task List connections
    // this.timerPresenter.on('taskListRequested', () => {
    //   this.taskListPresenter.showTaskList()
    // })

    this.settingsPresenter.on('settingsChanged', (settings) => {
      this.timerPresenter.updateSettings(settings)

      if (settings.musicEnabled !== undefined) {
        this.musicPresenter.updateConfig({ enabled: settings.musicEnabled })
      }
      if (settings.musicVolume !== undefined) {
        this.musicPresenter.updateConfig({ volume: settings.musicVolume / 100 })
      }
    })

    this.reportPresenter.on('requestStats', () => {
      const stats = this.timerPresenter.getStats()
      this.reportPresenter.updateReport(stats)
    })

    // Global shortcuts
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.code) {
          case 'Comma':
            e.preventDefault()
            this.settingsPresenter.toggleSettings()
            break
          case 'KeyT': // TODO: Task List
            e.preventDefault()
            console.log('Task List feature is under development')
            break
        }
      }
    })

    // Background tab handling
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden) {
        // Resume music if conditions are met
        if (this.timerPresenter.isRunning() && this.musicPresenter.isEnabled() && !this.musicPresenter.isPlaying()) {
          this.musicPresenter.resumeMusic()
        }
      }
    })

    // Save incomplete sessions before page unload
    window.addEventListener('beforeunload', (e) => {
      this.saveIncompleteSession()
    })

    // Also save on page hide (for mobile browsers)
    document.addEventListener('pagehide', (e) => {
      this.saveIncompleteSession()
    })

    // TODO: Task list toggle
    // this.setupTaskListToggle()

    // Expose debug methods globally for troubleshooting
    window.debugSettings = () => this.settingsPresenter.debugPanelState()
    window.fixSettings = () => this.settingsPresenter.emergencyReset()
  }

  // TODO: Task list setup
  // setupTaskListToggle() {
  //   const taskListBtn = document.getElementById('task-list-btn')
  //   if (taskListBtn) {
  //     taskListBtn.addEventListener('click', () => {
  //       this.taskListPresenter.toggleTaskList()
  //     })
  //   }
  // }

  getState() {
    return {
      timer: this.timerPresenter.getCurrentState(),
      music: this.musicPresenter.getState(),
      settings: this.settingsPresenter.getSettings()
    }
  }

  exportData() {
    return {
      settings: this.settingsPresenter.getSettings(),
      stats: this.timerPresenter.getStats()
    }
  }

  /**
   * Save incomplete session when user closes/reloads page
   */
  saveIncompleteSession() {
    try {
      const timerState = this.timerPresenter.getCurrentState()

      // Only save if timer is running and it's a work session
      if (timerState.isRunning && timerState.currentSession === 'work') {
        const incompleteSession = {
          sessionNumber: timerState.sessionNumber,
          completedSessions: timerState.completedSessions,
          totalFocusTime: timerState.totalFocusTime,
          currentSessionElapsed: timerState.totalTime - timerState.timeRemaining,
          timestamp: new Date().toISOString()
        }

        console.log('PomodoroApp: Saving incomplete session:', incompleteSession)

        // Save to daily reports immediately
        if (this.reportPresenter && incompleteSession.currentSessionElapsed > 0) {
          this.reportPresenter.saveIncompleteSession(incompleteSession)
        }
      }
    } catch (error) {
      console.error('PomodoroApp: Error saving incomplete session:', error)
    }
  }

  /**
   * Restore incomplete session from previous page load
   */
  restoreIncompleteSession() {
    try {
      const currentSession = this.reportPresenter.model.loadCurrentSession()

      if (currentSession && currentSession.type === 'work' && !currentSession.completed) {
        const now = new Date()
        const sessionStart = new Date(currentSession.startTime)
        const elapsedMinutes = Math.floor((now - sessionStart) / (1000 * 60))

        // Only restore if session was started recently (within last 2 hours)
        if (elapsedMinutes < 120) {
          console.log('PomodoroApp: Found incomplete session, adding to daily report')

          // Add the incomplete session time to daily reports
          const incompleteSession = {
            sessionNumber: 1,
            completedSessions: 0,
            totalFocusTime: currentSession.focusTime || 0,
            currentSessionElapsed: currentSession.focusTime || 0,
            timestamp: currentSession.startTime
          }

          if (incompleteSession.totalFocusTime > 0) {
            this.reportPresenter.saveIncompleteSession(incompleteSession)
          }
        }

        // Clear the incomplete session
        this.reportPresenter.model.clearCurrentSession()
      }
    } catch (error) {
      console.error('PomodoroApp: Error restoring incomplete session:', error)
    }
  }
}

document.addEventListener('DOMContentLoaded', async () => {
  document.body.classList.add('loaded')

  // Clean up task list artifacts
  const appElement = document.getElementById('app')
  if (appElement) {
    appElement.classList.remove('task-list-hidden')
    appElement.style.paddingLeft = ''
  }

  window.pomodoroApp = new PomodoroApp()
})
